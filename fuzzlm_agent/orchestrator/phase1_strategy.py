#!/usr/bin/env python3
"""Phase 1: Hot-Start Initial Strategy Generation
==============================================

This module handles "hot-start" initial strategy generation as described in
docs/workflow.md Phase 1. It extracts target features, queries knowledge base
for similar experiences, and uses LLM to generate an optimized initial strategy.

This is a standalone, simplified implementation focused on research prototype
principles: simple > clever, direct > abstract.

Key Components:
- Target feature extraction with heuristic analysis
- Knowledge base integration for experience retrieval
- LLM-driven strategy generation with structured prompts
- Rust mutator code generation for custom fuzzing logic
- Fallback mechanisms for robustness
"""

import logging
import re
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

import json_repair

from fuzzlm_agent.infrastructure.litellm_client import LiteLLMClient
from fuzzlm_agent.knowledge.simple_kb import SimpleKnowledgeBase
from fuzzlm_agent.orchestrator.campaign_orchestrator import CampaignContext
from fuzzlm_agent.prompts import PromptManager

logger = logging.getLogger(__name__)

# Initialize prompt manager (global instance for performance)
_prompt_manager = PromptManager()

# Constants
MAX_FILE_SIZE_MB = 10
DEFAULT_TIMEOUT_SECONDS = 60
MAX_SIMILAR_EXPERIENCES = 5


class StrategyGenerationError(Exception):
    """Phase 1 策略生成异常"""

    pass


class FeatureExtractionError(Exception):
    """特征提取异常"""

    pass


async def phase1_generate_strategy(
    ctx: CampaignContext,
    llm_client: LiteLLMClient,
    knowledge_base: Optional[SimpleKnowledgeBase],
) -> CampaignContext:
    """Generate initial fuzzing strategy using LLM and knowledge base.

    This function:
    1. Extracts target features from the source code
    2. Queries knowledge base for similar past experiences
    3. Builds a prompt with target features and similar cases
    4. Calls LLM to generate initial strategy
    5. Parses and validates the strategy response
    6. Updates ctx.strategy with the result

    Args:
        ctx: Campaign context containing target info and state
        llm_client: LLM client for strategy generation
        knowledge_base: Knowledge base for retrieving similar experiences (optional)

    Returns:
        Updated campaign context with generated strategy

    Raises:
        StrategyGenerationError: Critical failures in strategy generation
    """
    logger.info(f"Phase 1: Starting strategy generation for {ctx.target_path}")

    # Track phase timing
    import time

    phase_start = time.time()

    try:
        # Step 1: Extract target features
        target_features = await _extract_target_features(ctx.target_path)
        logger.info(
            f"Extracted {len(target_features.get('characteristics', []))} "
            f"characteristics from target"
        )

        # Step 2: Query knowledge base for similar experiences
        similar_experiences: List[Dict[str, Any]] = []
        if knowledge_base:
            try:
                similar_experiences = await _query_similar_experiences(
                    knowledge_base,
                    target_features,
                )
                logger.info(f"Found {len(similar_experiences)} similar experiences")
            except Exception as e:
                logger.warning(f"Knowledge base query failed: {e}")
                # Continue without similar experiences

        # Step 3: Build LLM prompt
        prompt = _build_strategy_prompt(
            target_features,
            similar_experiences,
            ctx.target_path,
        )

        # Step 4: Call LLM to generate strategy
        logger.info("Calling LLM for strategy generation...")
        llm_response = await _call_llm_with_retry(llm_client, prompt)

        if not llm_response:
            raise StrategyGenerationError("Empty LLM response")

        # Step 5: Parse and validate strategy
        strategy = _parse_llm_strategy(llm_response, ctx)
        logger.info(f"Successfully parsed strategy: {strategy['name']}")

        # Step 5.5: Generate Rust mutator code if enabled
        if ctx.config.get("fuzzing", {}).get("enable_llm_mutator_generation", True):
            try:
                logger.info("Generating Rust mutator code...")
                rust_code = await _generate_rust_mutator_code(
                    llm_client,
                    strategy,
                    target_features,
                )
                if rust_code:
                    strategy["rust_mutator_code"] = rust_code
                    logger.info(f"Generated {len(rust_code)} chars of Rust code")
            except Exception as e:
                logger.warning(f"Rust mutator generation failed: {e}")
                # Continue without custom mutator

        # Step 6: Update context with strategy
        ctx.strategy = strategy

        # Update timing
        ctx.phase_timings["phase1_strategy"] = time.time() - phase_start

        logger.info(
            f"Phase 1 completed in {ctx.phase_timings['phase1_strategy']:.2f}s "
            f"with strategy: {strategy['name']}"
        )
        return ctx

    except Exception as e:
        logger.error(f"Phase 1 strategy generation failed: {e}", exc_info=True)

        # Create fallback strategy
        logger.info("Creating fallback strategy...")
        try:
            fallback_strategy = _create_fallback_strategy(ctx)
            ctx.strategy = fallback_strategy
            ctx.metadata["strategy_fallback"] = True
            ctx.metadata["strategy_error"] = str(e)
            return ctx
        except Exception as fallback_error:
            # If even fallback fails, raise the original error
            raise StrategyGenerationError(
                f"Strategy generation failed: {e}, "
                f"fallback also failed: {fallback_error}"
            ) from e


async def _extract_target_features(target_path: str) -> Dict[str, Any]:
    """从目标源代码提取特征

    Args:
        target_path: 目标文件路径

    Returns:
        Dict[str, Any]: 特征字典，包含文件类型、语言、特征等

    Raises:
        FeatureExtractionError: 特征提取失败
    """
    path = Path(target_path)

    # 基本特征提取
    features: Dict[str, Any] = {
        "file_type": (
            "source"
            if path.suffix in [".c", ".cpp", ".cc", ".cxx", ".c++"]
            else "binary"
        ),
        "language": _detect_language(path.suffix),
        "file_name": path.name,
        "file_size": 0,
        "characteristics": [],
        "imports": [],
        "functions": [],
        "api_calls": [],
    }

    # 读取文件以提取更多特征
    try:
        if not path.exists():
            raise FeatureExtractionError(f"Target file not found: {target_path}")

        # 检查文件大小
        file_size_mb = path.stat().st_size / (1024 * 1024)
        if file_size_mb > MAX_FILE_SIZE_MB:
            logger.warning(
                f"Target file is {file_size_mb:.1f}MB, "
                f"exceeding {MAX_FILE_SIZE_MB}MB limit"
            )
            features["complexity_level"] = "high"
            features["file_size"] = path.stat().st_size
            return features

        if path.suffix in [".c", ".cpp", ".cc", ".cxx", ".c++"]:
            content = path.read_text(encoding="utf-8", errors="ignore")
            features["file_size"] = len(content)

            # 启发式分析
            features.update(_analyze_source_code(content))

            # 估计复杂度
            features["complexity_level"] = _estimate_complexity(features)

    except Exception as e:
        logger.warning(f"Failed to read target file for analysis: {e}")
        features["complexity_level"] = "medium"
        features["error"] = str(e)

    return features


def _detect_language(suffix: str) -> str:
    """检测编程语言"""
    language_map = {
        ".c": "c",
        ".cpp": "cpp",
        ".cc": "cpp",
        ".cxx": "cpp",
        ".c++": "cpp",
        ".rs": "rust",
        ".go": "go",
        ".py": "python",
        ".js": "javascript",
    }
    return language_map.get(suffix.lower(), "unknown")


def _analyze_source_code(content: str) -> Dict[str, Any]:
    """分析源代码内容

    Args:
        content: 源代码内容

    Returns:
        Dict[str, Any]: 分析结果
    """
    analysis: Dict[str, Any] = {
        "characteristics": [],
        "imports": [],
        "functions": [],
        "api_calls": [],
        "line_count": content.count("\n"),
    }

    # 内存管理
    if any(func in content for func in ["malloc", "free", "calloc", "realloc"]):
        analysis["characteristics"].append("manual_memory_management")

    # 字符串操作
    unsafe_string_funcs = ["strcpy", "strcat", "sprintf", "gets"]
    if any(func in content for func in unsafe_string_funcs):
        analysis["characteristics"].append("unsafe_string_operations")

    safe_string_funcs = ["strncpy", "strncat", "snprintf"]
    if any(func in content for func in safe_string_funcs):
        analysis["characteristics"].append("safe_string_operations")

    # 文件IO
    if any(func in content for func in ["fopen", "fread", "fwrite", "fclose"]):
        analysis["characteristics"].append("file_io")

    # 网络
    if any(
        func in content for func in ["socket", "bind", "listen", "accept", "connect"]
    ):
        analysis["characteristics"].append("network_operations")

    # 多线程
    if "pthread" in content or "std::thread" in content:
        analysis["characteristics"].append("multithreading")

    # 加密
    if any(crypto in content for crypto in ["crypt", "AES", "RSA", "SHA", "MD5"]):
        analysis["characteristics"].append("cryptography")

    # 提取函数名 (简单正则)
    function_pattern = re.compile(r"\b(\w+)\s*\([^)]*\)\s*{")
    functions = function_pattern.findall(content)
    analysis["functions"] = list(set(functions[:20]))  # 限制数量

    # 提取导入 (简单正则)
    import_pattern = re.compile(r"#include\s*[<\"]([^>\"]+)[>\"]")
    imports = import_pattern.findall(content)
    analysis["imports"] = list(set(imports[:20]))  # 限制数量

    return analysis


def _estimate_complexity(features: Dict[str, Any]) -> str:
    """估计代码复杂度

    Args:
        features: 特征字典

    Returns:
        str: 复杂度级别 (low/medium/high)
    """
    score = 0

    # 基于行数
    line_count = features.get("line_count", 0)
    if line_count < 100:
        score += 0
    elif line_count < 500:
        score += 1
    elif line_count < 1000:
        score += 2
    else:
        score += 3

    # 基于特征
    characteristics = features.get("characteristics", [])
    complexity_characteristics = [
        "manual_memory_management",
        "unsafe_string_operations",
        "network_operations",
        "multithreading",
        "cryptography",
    ]
    score += sum(1 for char in complexity_characteristics if char in characteristics)

    # 基于函数数量
    function_count = len(features.get("functions", []))
    if function_count > 15:
        score += 2
    elif function_count > 10:
        score += 1

    # 确定复杂度
    if score <= 2:
        return "low"
    elif score <= 5:
        return "medium"
    else:
        return "high"


async def _query_similar_experiences(
    knowledge_base: SimpleKnowledgeBase,
    target_features: Dict[str, Any],
) -> List[Dict[str, Any]]:
    """查询知识库以获取相似的模糊测试经验

    Args:
        knowledge_base: 知识库实例
        target_features: 目标特征

    Returns:
        List[Dict[str, Any]]: 相似经验列表
    """
    try:
        # 检查知识库是否支持经验搜索
        if not hasattr(knowledge_base, "search_experiences"):
            logger.debug("Knowledge base does not support experience search")
            return []

        # 构建搜索查询
        query = {
            "language": target_features.get("language", "unknown"),
            "complexity_level": target_features.get("complexity_level", "medium"),
            "characteristics": target_features.get("characteristics", []),
        }

        # 搜索相似经验
        experiences = await knowledge_base.search_experiences(
            query, limit=MAX_SIMILAR_EXPERIENCES
        )

        # 过滤和排序
        filtered_experiences = [
            exp
            for exp in experiences
            if exp.get("success_metrics", {}).get("final_coverage", 0) > 0.1
        ]

        # 按覆盖率排序
        filtered_experiences.sort(
            key=lambda x: x.get("success_metrics", {}).get("final_coverage", 0),
            reverse=True,
        )

        return filtered_experiences[:MAX_SIMILAR_EXPERIENCES]

    except Exception as e:
        logger.warning(f"Failed to query knowledge base: {e}")
        return []


def _build_strategy_prompt(
    target_features: Dict[str, Any],
    similar_experiences: List[Dict[str, Any]],
    target_path: str,
) -> str:
    """构建LLM策略生成提示

    Args:
        target_features: 目标特征
        similar_experiences: 相似经验
        target_path: 目标路径

    Returns:
        str: 生成的提示
    """
    # 格式化相似经验摘要
    experience_summary = ""
    if similar_experiences:
        experience_summary = "\n\nRelevant past fuzzing campaigns:\n"
        for i, exp in enumerate(similar_experiences[:3]):
            metrics = exp.get("success_metrics", {})
            coverage = metrics.get("final_coverage", 0)
            crashes = metrics.get("unique_crashes", 0)
            strategy_info = exp.get("strategy", {})
            mutators = strategy_info.get("mutators", [])

            experience_summary += (
                f"\nCase {i + 1}:\n"
                f"  - Coverage: {coverage:.1%}\n"
                f"  - Unique crashes: {crashes}\n"
            )

            if mutators:
                mutator_types = [m.get("type", "unknown") for m in mutators[:3]]
                experience_summary += f"  - Mutators: {', '.join(mutator_types)}\n"

    try:
        # 使用 PromptManager 生成提示
        return _prompt_manager.get_prompt(
            "strategy.generation",
            target_path=target_path,
            language=target_features.get("language", "unknown"),
            complexity_level=target_features.get("complexity_level", "medium"),
            characteristics=target_features.get("characteristics", []),
            functions=target_features.get("functions", [])[:5],  # 限制函数数量
            imports=target_features.get("imports", [])[:5],  # 限制导入数量
            experience_summary=experience_summary.strip(),
        )

    except Exception as e:
        logger.error(f"Failed to generate strategy prompt: {e}")
        # 回退到基本提示
        return _create_fallback_prompt(target_features, target_path, experience_summary)


def _create_fallback_prompt(
    target_features: Dict[str, Any], target_path: str, experience_summary: str
) -> str:
    """创建回退提示"""
    characteristics = ", ".join(target_features.get("characteristics", ["general"]))
    return f"""You are an expert fuzzing engineer. Generate an optimized initial fuzzing strategy for {Path(target_path).name}.

Target Information:
- Language: {target_features.get("language", "unknown")}
- Complexity: {target_features.get("complexity_level", "medium")}
- Characteristics: {characteristics}
- Line count: {target_features.get("line_count", "unknown")}
{experience_summary}

Generate a JSON response with the following structure:
{{
    "strategy_name": "descriptive name",
    "mutators": [{{"type": "mutator_type", "config": {{}}}}],
    "scheduler": {{"type": "scheduler_type", "config": {{}}}},
    "feedback": {{"type": "feedback_type", "config": {{}}}},
    "reasoning": "explanation of strategy choices"
}}

Focus on effective mutators for the target's characteristics."""


async def _call_llm_with_retry(
    llm_client: LiteLLMClient, prompt: str, max_retries: int = 3
) -> str:
    """使用重试机制调用LLM

    Args:
        llm_client: LLM客户端
        prompt: 提示
        max_retries: 最大重试次数

    Returns:
        str: LLM响应

    Raises:
        Exception: 所有重试失败后
    """
    last_error = None

    for attempt in range(max_retries):
        try:
            response = llm_client.generate(prompt)
            if response and response.strip():
                return response
            else:
                logger.warning(f"Empty LLM response on attempt {attempt + 1}")

        except Exception as e:
            last_error = e
            logger.warning(
                f"LLM call failed on attempt {attempt + 1}/{max_retries}: {e}"
            )

            # 指数退避
            if attempt < max_retries - 1:
                import asyncio

                await asyncio.sleep(2**attempt)

    raise Exception(f"All LLM retries failed. Last error: {last_error}")


def _parse_llm_strategy(response: str, ctx: CampaignContext) -> Dict[str, Any]:
    """解析LLM响应为策略字典

    Args:
        response: LLM响应文本
        ctx: Campaign上下文

    Returns:
        Dict[str, Any]: 解析后的策略

    Raises:
        ValueError: 解析失败时
    """
    try:
        # 使用 json_repair 解析
        strategy_data = json_repair.loads(response)

        if not isinstance(strategy_data, dict):
            raise ValueError("Parsed strategy is not a JSON object")

        # 验证必需字段
        required_fields = ["mutators"]
        missing_fields = [
            field for field in required_fields if field not in strategy_data
        ]

        if missing_fields:
            logger.warning(f"Missing required fields: {missing_fields}")

        # 提取和验证字段
        strategy_name = strategy_data.get(
            "strategy_name",
            f"AI-Generated Strategy for {Path(ctx.target_path).stem}",
        )

        # 验证和标准化mutators
        mutators = _validate_mutators(strategy_data.get("mutators", []))

        # 验证scheduler
        scheduler = _validate_scheduler(strategy_data.get("scheduler", {}))

        # 验证feedback
        feedback = _validate_feedback(strategy_data.get("feedback", {}))

        # 返回标准化的策略
        return {
            "name": strategy_name,
            "mutators": mutators,
            "scheduler": scheduler,
            "feedback": feedback,
            "metadata": {
                "generation_method": "llm_driven",
                "generation_timestamp": datetime.now(timezone.utc).isoformat(),
                "llm_reasoning": strategy_data.get("reasoning", ""),
                "target_characteristics": strategy_data.get(
                    "target_characteristics", []
                ),
            },
        }

    except Exception as e:
        logger.error(f"Failed to parse LLM strategy: {e}")
        logger.debug(f"Raw response: {response[:500]}...")
        raise ValueError(f"Strategy parsing failed: {e}")


def _validate_mutators(mutators: List[Any]) -> List[Dict[str, Any]]:
    """验证和标准化mutators"""
    valid_mutator_types = {
        "havoc",
        "splice",
        "radamsa",
        "bit_flip",
        "byte_flip",
        "interesting_values",
        "dictionary",
        "crossover",
        "custom",
    }

    validated_mutators = []

    # 如果没有mutators，使用默认值
    if not mutators:
        return [
            {"type": "havoc", "config": {"max_len": 1024}},
            {"type": "splice", "config": {}},
            {"type": "bit_flip", "config": {}},
        ]

    for mutator in mutators:
        if isinstance(mutator, dict):
            mutator_type = mutator.get("type", "").lower()
            if mutator_type in valid_mutator_types:
                validated_mutators.append(
                    {"type": mutator_type, "config": mutator.get("config", {})}
                )
            else:
                logger.warning(f"Invalid mutator type: {mutator_type}")

    # 确保至少有一个mutator
    if not validated_mutators:
        validated_mutators.append({"type": "havoc", "config": {"max_len": 1024}})

    return validated_mutators


def _validate_scheduler(scheduler: Any) -> Dict[str, Any]:
    """验证和标准化scheduler"""
    valid_scheduler_types = {"queue", "weighted", "adaptive", "probabilistic"}

    if not isinstance(scheduler, dict):
        return {"type": "weighted", "config": {}}

    scheduler_type = scheduler.get("type", "").lower()
    if scheduler_type not in valid_scheduler_types:
        logger.warning(f"Invalid scheduler type: {scheduler_type}, using weighted")
        scheduler_type = "weighted"

    return {"type": scheduler_type, "config": scheduler.get("config", {})}


def _validate_feedback(feedback: Any) -> Dict[str, Any]:
    """验证和标准化feedback"""
    valid_feedback_types = {"max_map", "coverage", "crash", "combined", "differential"}

    if not isinstance(feedback, dict):
        return {"type": "max_map", "config": {}}

    feedback_type = feedback.get("type", "").lower()
    if feedback_type not in valid_feedback_types:
        logger.warning(f"Invalid feedback type: {feedback_type}, using max_map")
        feedback_type = "max_map"

    return {"type": feedback_type, "config": feedback.get("config", {})}


def _create_fallback_strategy(ctx: CampaignContext) -> Dict[str, Any]:
    """创建一个简单的回退策略

    Args:
        ctx: Campaign上下文

    Returns:
        Dict[str, Any]: 回退策略
    """
    target_name = Path(ctx.target_path).stem

    # 根据目标类型选择默认mutators
    file_suffix = Path(ctx.target_path).suffix.lower()

    if file_suffix in [".c", ".cpp", ".cc"]:
        # C/C++程序的默认策略
        mutators = [
            {"type": "havoc", "config": {"max_len": 1024}},
            {"type": "splice", "config": {}},
            {"type": "bit_flip", "config": {}},
            {"type": "interesting_values", "config": {}},
        ]
    else:
        # 通用默认策略
        mutators = [
            {"type": "havoc", "config": {"max_len": 1024}},
            {"type": "splice", "config": {}},
        ]

    return {
        "name": f"Fallback Strategy for {target_name}",
        "mutators": mutators,
        "scheduler": {"type": "weighted", "config": {}},
        "feedback": {"type": "max_map", "config": {}},
        "metadata": {
            "generation_method": "fallback",
            "generation_timestamp": datetime.now(timezone.utc).isoformat(),
            "reason": "LLM strategy generation failed",
        },
    }


async def _generate_rust_mutator_code(
    llm_client: LiteLLMClient,
    strategy: Dict[str, Any],
    target_features: Dict[str, Any],
) -> str:
    """基于策略生成Rust mutator代码

    这是研究原型的最小实现。
    生成一个可以被LibAFL编译和加载的基本自定义mutator。

    Args:
        llm_client: LLM客户端
        strategy: 策略字典
        target_features: 目标特征

    Returns:
        str: Rust代码或空字符串
    """
    try:
        # 从策略中提取mutator类型
        mutator_types = [m.get("type", "havoc") for m in strategy.get("mutators", [])]

        # 检查是否需要自定义mutator
        if "custom" not in mutator_types and not any(
            char in target_features.get("characteristics", [])
            for char in ["cryptography", "network_operations", "file_io"]
        ):
            logger.info("No custom mutator needed for this target")
            return ""

        # 使用 PromptManager 生成代码生成提示
        prompt = _prompt_manager.get_prompt(
            "code_generation.rust_mutator",
            mutator_types=mutator_types,
            characteristics=target_features.get("characteristics", ["general"]),
            complexity_level=target_features.get("complexity_level", "medium"),
            language=target_features.get("language", "c"),
        )

        # 调用LLM生成代码
        logger.info("Calling LLM to generate Rust mutator code...")
        code_response = await _call_llm_with_retry(llm_client, prompt, max_retries=2)

        # 提取代码
        code = _extract_rust_code(code_response)

        if not code:
            logger.warning("Failed to extract valid Rust code from LLM response")
            return ""

        # 基本验证
        if not _validate_rust_code(code):
            logger.warning("Generated Rust code failed validation")
            return ""

        logger.info(f"Generated Rust mutator code ({len(code)} chars)")
        return code

    except Exception as e:
        logger.error(f"Failed to generate Rust mutator code: {e}")

        # 尝试回退提示
        try:
            fallback_prompt = _create_rust_fallback_prompt(
                mutator_types, target_features
            )
            code_response = await _call_llm_with_retry(
                llm_client, fallback_prompt, max_retries=1
            )
            code = _extract_rust_code(code_response)

            if code and _validate_rust_code(code):
                return code

        except Exception as fallback_error:
            logger.error(f"Fallback Rust code generation also failed: {fallback_error}")

        return ""


def _create_rust_fallback_prompt(
    mutator_types: List[str], target_features: Dict[str, Any]
) -> str:
    """创建Rust代码生成的回退提示"""
    characteristics = ", ".join(target_features.get("characteristics", ["general"]))
    return f"""Generate a LibAFL custom mutator in Rust.

Target characteristics: {characteristics}
Required mutator types: {', '.join(mutator_types)}

Generate a complete Rust implementation that:
1. Implements the LibAFL Mutator trait
2. Can be compiled as a dynamic library
3. Includes appropriate imports and dependencies
4. Has basic error handling

Generate ONLY the Rust code, no explanations."""


def _extract_rust_code(response: str) -> str:
    """从LLM响应中提取Rust代码

    Args:
        response: LLM响应文本

    Returns:
        str: 提取的Rust代码
    """
    # 查找Rust代码块
    if "```rust" in response:
        start = response.find("```rust") + 7
        end = response.find("```", start)
        if end > start:
            return response[start:end].strip()

    # 查找通用代码块
    if "```" in response:
        # 可能有多个代码块，找到看起来像Rust的
        import re

        code_blocks = re.findall(r"```(?:\w+)?\n?(.*?)```", response, re.DOTALL)

        for block in code_blocks:
            if _looks_like_rust(block):
                return str(block).strip()

    # 如果没有代码块，假设整个响应是代码
    # (但只有看起来像Rust代码时)
    if _looks_like_rust(response):
        return response.strip()

    return ""


def _looks_like_rust(code: str) -> bool:
    """检查代码是否看起来像Rust"""
    rust_indicators = [
        "impl",
        "fn",
        "let",
        "mut",
        "pub",
        "struct",
        "trait",
        "use",
        "mod",
        "->",
        "&",
        "Vec<",
        "Result<",
    ]
    return any(indicator in code for indicator in rust_indicators)


def _validate_rust_code(code: str) -> bool:
    """基本验证Rust代码

    Args:
        code: Rust代码

    Returns:
        bool: 是否有效
    """
    if not code or len(code) < 50:
        return False

    # 检查必需的结构
    required_patterns = [
        r"impl.*Mutator",  # 实现Mutator trait
        r"fn\s+mutate",  # mutate函数
    ]

    for pattern in required_patterns:
        if not re.search(pattern, code, re.IGNORECASE | re.DOTALL):
            logger.debug(f"Missing required pattern: {pattern}")
            return False

    # 检查危险模式
    dangerous_patterns = [
        r"std::process::Command",
        r"std::fs::remove",
        r"unsafe\s*\{[^}]*system\(",
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, code):
            logger.warning(f"Dangerous pattern found: {pattern}")
            return False

    return True
